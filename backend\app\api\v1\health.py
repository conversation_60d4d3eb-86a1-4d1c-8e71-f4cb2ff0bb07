"""
健康检查相关API路由
"""
import logging
from fastapi import APIRouter, Depends, HTTPException

from backend.app.dependencies import get_rag_service
from backend.app.models.responses import StatusResponse
from backend.app.rag_service import RAGService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status", response_model=StatusResponse)
async def get_status(rag_service: RAGService = Depends(get_rag_service)):
    """获取系统状态"""
    try:
        status = rag_service.get_status()
        return StatusResponse(**status)
        
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")
